{"schemes": ["http", "https"], "swagger": "2.0", "info": {"description": "A simple Go API with JWT authentication, role-based access control, WebSocket chat, and product management.", "title": "SmarApp API", "termsOfService": "http://swagger.io/terms/", "contact": {"name": "API Support", "url": "http://www.swagger.io/support", "email": "<EMAIL>"}, "license": {"name": "MIT", "url": "https://opensource.org/licenses/MIT"}, "version": "1.0"}, "host": "localhost:8080", "basePath": "/api/v1", "paths": {"/auth/login": {"post": {"description": "Authenticate user with email and password", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Authentication"], "summary": "Login user", "parameters": [{"description": "User login credentials", "name": "credentials", "in": "body", "required": true, "schema": {"$ref": "#/definitions/models.LoginRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.LoginResponse"}}, "400": {"description": "Bad Request", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "401": {"description": "Unauthorized", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "500": {"description": "Internal Server Error", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/auth/register": {"post": {"description": "Register a new user with username, email, password and optional role", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Authentication"], "summary": "Register a new user", "parameters": [{"description": "User registration data", "name": "user", "in": "body", "required": true, "schema": {"$ref": "#/definitions/models.RegisterRequest"}}], "responses": {"201": {"description": "Created", "schema": {"$ref": "#/definitions/models.LoginResponse"}}, "400": {"description": "Bad Request", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "409": {"description": "Conflict", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "500": {"description": "Internal Server Error", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/orders": {"post": {"security": [{"BearerAuth": []}], "description": "Create a new order to purchase a product, automatically reduces stock", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Orders"], "summary": "Create a new order (Buy a product)", "parameters": [{"description": "Order data", "name": "order", "in": "body", "required": true, "schema": {"$ref": "#/definitions/models.CreateOrderRequest"}}], "responses": {"201": {"description": "Created", "schema": {"$ref": "#/definitions/models.OrderResponse"}}, "400": {"description": "Bad Request", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "401": {"description": "Unauthorized", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "404": {"description": "Not Found", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "500": {"description": "Internal Server Error", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/products": {"get": {"description": "Get a list of all products", "produces": ["application/json"], "tags": ["Products"], "summary": "Get all products", "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/models.Product"}}}, "500": {"description": "Internal Server Error", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}, "post": {"security": [{"BearerAuth": []}], "description": "Create a new product with name, description, price and stock", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Products"], "summary": "Create a new product (Admin only)", "parameters": [{"description": "Product data", "name": "product", "in": "body", "required": true, "schema": {"$ref": "#/definitions/models.CreateProductRequest"}}], "responses": {"201": {"description": "Created", "schema": {"$ref": "#/definitions/models.Product"}}, "400": {"description": "Bad Request", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "401": {"description": "Unauthorized", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "403": {"description": "Forbidden", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "500": {"description": "Internal Server Error", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/profile": {"get": {"security": [{"BearerAuth": []}], "description": "Get the profile of the authenticated user", "produces": ["application/json"], "tags": ["Authentication"], "summary": "Get user profile", "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.User"}}, "401": {"description": "Unauthorized", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "404": {"description": "Not Found", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}}, "definitions": {"models.CreateOrderRequest": {"type": "object", "required": ["product_id", "quantity"], "properties": {"product_id": {"type": "integer"}, "quantity": {"type": "integer"}}}, "models.CreateProductRequest": {"type": "object", "required": ["description", "name", "price", "stock"], "properties": {"description": {"type": "string", "maxLength": 500, "minLength": 1}, "name": {"type": "string", "maxLength": 100, "minLength": 1}, "price": {"type": "number"}, "stock": {"type": "integer", "minimum": 0}}}, "models.LoginRequest": {"type": "object", "required": ["email", "password"], "properties": {"email": {"type": "string"}, "password": {"type": "string", "minLength": 6}}}, "models.LoginResponse": {"type": "object", "properties": {"token": {"type": "string"}, "user": {"$ref": "#/definitions/models.User"}}}, "models.Order": {"type": "object", "properties": {"created_at": {"type": "string"}, "id": {"type": "integer"}, "price": {"description": "Price at time of purchase", "type": "number"}, "product_id": {"type": "integer"}, "quantity": {"type": "integer"}, "status": {"$ref": "#/definitions/models.OrderStatus"}, "total": {"type": "number"}, "updated_at": {"type": "string"}, "user_id": {"type": "integer"}}}, "models.OrderResponse": {"type": "object", "properties": {"order": {"$ref": "#/definitions/models.Order"}, "product": {"$ref": "#/definitions/models.Product"}}}, "models.OrderStatus": {"type": "string", "enum": ["pending", "completed", "cancelled"], "x-enum-varnames": ["OrderStatusPending", "OrderStatusCompleted", "OrderStatusCancelled"]}, "models.Product": {"type": "object", "properties": {"created_at": {"type": "string"}, "created_by": {"type": "integer"}, "description": {"type": "string"}, "id": {"type": "integer"}, "name": {"type": "string"}, "price": {"type": "number"}, "stock": {"type": "integer"}, "updated_at": {"type": "string"}}}, "models.RegisterRequest": {"type": "object", "required": ["email", "password", "username"], "properties": {"email": {"type": "string"}, "password": {"type": "string", "minLength": 6}, "role": {"description": "Optional, defaults to user", "allOf": [{"$ref": "#/definitions/models.Role"}]}, "username": {"type": "string", "maxLength": 50, "minLength": 3}}}, "models.Role": {"type": "string", "enum": ["admin", "user"], "x-enum-varnames": ["RoleAdmin", "RoleUser"]}, "models.User": {"type": "object", "properties": {"created_at": {"type": "string"}, "email": {"type": "string"}, "id": {"type": "integer"}, "role": {"$ref": "#/definitions/models.Role"}, "updated_at": {"type": "string"}, "username": {"type": "string"}}}}, "securityDefinitions": {"BearerAuth": {"description": "Type \"Bearer\" followed by a space and JWT token.", "type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header"}}}