
<!DOCTYPE html>
<html>
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
		<title>server: Go Coverage Report</title>
		<style>
			body {
				background: black;
				color: rgb(80, 80, 80);
			}
			body, pre, #legend span {
				font-family: Menlo, monospace;
				font-weight: bold;
			}
			#topbar {
				background: black;
				position: fixed;
				top: 0; left: 0; right: 0;
				height: 42px;
				border-bottom: 1px solid rgb(80, 80, 80);
			}
			#content {
				margin-top: 50px;
			}
			#nav, #legend {
				float: left;
				margin-left: 10px;
			}
			#legend {
				margin-top: 12px;
			}
			#nav {
				margin-top: 10px;
			}
			#legend span {
				margin: 0 5px;
			}
			.cov0 { color: rgb(192, 0, 0) }
.cov1 { color: rgb(128, 128, 128) }
.cov2 { color: rgb(116, 140, 131) }
.cov3 { color: rgb(104, 152, 134) }
.cov4 { color: rgb(92, 164, 137) }
.cov5 { color: rgb(80, 176, 140) }
.cov6 { color: rgb(68, 188, 143) }
.cov7 { color: rgb(56, 200, 146) }
.cov8 { color: rgb(44, 212, 149) }
.cov9 { color: rgb(32, 224, 152) }
.cov10 { color: rgb(20, 236, 155) }

		</style>
	</head>
	<body>
		<div id="topbar">
			<div id="nav">
				<select id="files">
				
				<option value="file0">smarapp-api/cmd/server/main.go (0.0%)</option>
				
				<option value="file1">smarapp-api/config/config.go (0.0%)</option>
				
				<option value="file2">smarapp-api/database/database.go (77.3%)</option>
				
				<option value="file3">smarapp-api/docs/docs.go (0.0%)</option>
				
				<option value="file4">smarapp-api/handlers/auth.go (70.7%)</option>
				
				<option value="file5">smarapp-api/handlers/chat.go (74.1%)</option>
				
				<option value="file6">smarapp-api/handlers/order.go (76.1%)</option>
				
				<option value="file7">smarapp-api/handlers/product.go (82.2%)</option>
				
				<option value="file8">smarapp-api/middleware/auth.go (100.0%)</option>
				
				<option value="file9">smarapp-api/testutil/database.go (53.3%)</option>
				
				<option value="file10">smarapp-api/websocket/client.go (0.0%)</option>
				
				<option value="file11">smarapp-api/websocket/hub.go (1.9%)</option>
				
				</select>
			</div>
			<div id="legend">
				<span>not tracked</span>
			
				<span class="cov0">no coverage</span>
				<span class="cov1">low coverage</span>
				<span class="cov2">*</span>
				<span class="cov3">*</span>
				<span class="cov4">*</span>
				<span class="cov5">*</span>
				<span class="cov6">*</span>
				<span class="cov7">*</span>
				<span class="cov8">*</span>
				<span class="cov9">*</span>
				<span class="cov10">high coverage</span>
			
			</div>
		</div>
		<div id="content">
		
		<pre class="file" id="file0" style="display: none">package main

import (
        "log"
        "smarapp-api/config"
        "smarapp-api/database"
        _ "smarapp-api/docs"
        "smarapp-api/handlers"
        "smarapp-api/middleware"
        "smarapp-api/websocket"

        "github.com/gin-contrib/cors"
        "github.com/gin-gonic/gin"
        swaggerFiles "github.com/swaggo/files"
        ginSwagger "github.com/swaggo/gin-swagger"
)

// @title SmarApp API
// @version 1.0
// @description A simple Go API with JWT authentication, role-based access control, WebSocket chat, and product management.
// @termsOfService http://swagger.io/terms/

// @contact.name API Support
// @contact.url http://www.swagger.io/support
// @contact.email <EMAIL>

// @license.name MIT
// @license.url https://opensource.org/licenses/MIT

// @host 127.0.0.1:8081
// @BasePath /api/v1
// @schemes http https

// @securityDefinitions.apikey BearerAuth
// @in header
// @name Authorization
// @description Type "Bearer" followed by a space and JWT token.

func main() <span class="cov0" title="0">{
        // Load configuration
        cfg := config.LoadConfig()

        // Initialize database
        if err := database.InitDB(cfg.DatabaseURL); err != nil </span><span class="cov0" title="0">{
                log.Fatalf("Failed to initialize database: %v", err)
        }</span>
        <span class="cov0" title="0">defer database.CloseDB()

        // Initialize WebSocket hub
        hub := websocket.NewHub()
        go hub.Run()

        // Initialize handlers
        authHandler := handlers.NewAuthHandler(cfg.JWTSecret)
        productHandler := handlers.NewProductHandler()
        orderHandler := handlers.NewOrderHandler()
        chatHandler := handlers.NewChatHandler(hub)

        // Setup Gin router
        r := gin.Default()

        // CORS middleware - Allow all origins for development
        r.Use(cors.New(cors.Config{
                AllowOriginFunc: func(origin string) bool </span><span class="cov0" title="0">{
                        return true // Allow all origins
                }</span>,
                AllowMethods:     []string{"GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"},
                AllowHeaders:     []string{"Origin", "Content-Length", "Content-Type", "Authorization", "X-Requested-With"},
                ExposeHeaders:    []string{"Content-Length", "Authorization"},
                AllowCredentials: true,
                MaxAge:           12 * 3600, // 12 hours
        }))

        // Health check
        <span class="cov0" title="0">r.GET("/health", func(c *gin.Context) </span><span class="cov0" title="0">{
                c.JSON(200, gin.H{"status": "ok"})
        }</span>)

        // Swagger documentation
        <span class="cov0" title="0">r.GET("/docs/*any", ginSwagger.WrapHandler(swaggerFiles.Handler, ginSwagger.URL("http://127.0.0.1:8081/docs/doc.json")))

        // Public routes
        api := r.Group("/api/v1")
        </span><span class="cov0" title="0">{
                // Auth routes
                auth := api.Group("/auth")
                </span><span class="cov0" title="0">{
                        auth.POST("/register", authHandler.Register)
                        auth.POST("/login", authHandler.Login)
                }</span>

                // Public product routes
                <span class="cov0" title="0">products := api.Group("/products")
                </span><span class="cov0" title="0">{
                        products.GET("", productHandler.GetProducts)
                        products.GET("/:id", productHandler.GetProduct)
                }</span>
        }

        // Protected routes
        <span class="cov0" title="0">protected := api.Group("/")
        protected.Use(middleware.AuthMiddleware(cfg.JWTSecret))
        </span><span class="cov0" title="0">{
                // User profile
                protected.GET("/profile", authHandler.GetProfile)

                // Product management (admin only)
                adminProducts := protected.Group("/products")
                adminProducts.Use(middleware.AdminMiddleware())
                </span><span class="cov0" title="0">{
                        adminProducts.POST("", productHandler.CreateProduct)
                        adminProducts.PUT("/:id", productHandler.UpdateProduct)
                        adminProducts.DELETE("/:id", productHandler.DeleteProduct)
                }</span>

                // Order management
                <span class="cov0" title="0">orders := protected.Group("/orders")
                </span><span class="cov0" title="0">{
                        orders.POST("", orderHandler.CreateOrder)
                        orders.GET("", orderHandler.GetUserOrders)
                        orders.GET("/:id", orderHandler.GetOrder)
                }</span>

                // Admin order management
                <span class="cov0" title="0">adminOrders := protected.Group("/admin/orders")
                adminOrders.Use(middleware.AdminMiddleware())
                </span><span class="cov0" title="0">{
                        adminOrders.GET("", orderHandler.GetAllOrders)
                }</span>

                // Chat routes
                <span class="cov0" title="0">chat := protected.Group("/chat")
                </span><span class="cov0" title="0">{
                        chat.GET("/ws", chatHandler.HandleWebSocket)
                        chat.GET("/history", chatHandler.GetChatHistory)
                }</span>
        }

        <span class="cov0" title="0">log.Printf("Server starting on port %s", cfg.Port)
        if err := r.Run(":" + cfg.Port); err != nil </span><span class="cov0" title="0">{
                log.Fatalf("Failed to start server: %v", err)
        }</span>
}
</pre>
		
		<pre class="file" id="file1" style="display: none">package config

import (
        "os"
)

type Config struct {
        Port        string
        DatabaseURL string
        JWTSecret   string
}

func LoadConfig() *Config <span class="cov0" title="0">{
        return &amp;Config{
                Port:        getEnv("PORT", "8081"),
                DatabaseURL: getEnv("DATABASE_URL", "./smarapp.db"),
                JWTSecret:   getEnv("JWT_SECRET", "your-secret-key-change-this-in-production"),
        }
}</span>

func getEnv(key, defaultValue string) string <span class="cov0" title="0">{
        if value := os.Getenv(key); value != "" </span><span class="cov0" title="0">{
                return value
        }</span>
        <span class="cov0" title="0">return defaultValue</span>
}
</pre>
		
		<pre class="file" id="file2" style="display: none">package database

import (
        "database/sql"
        "fmt"
        "log"

        _ "github.com/mattn/go-sqlite3"
)

var DB *sql.DB

func InitDB(dataSourceName string) error <span class="cov6" title="15">{
        var err error
        DB, err = sql.Open("sqlite3", dataSourceName)
        if err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("failed to open database: %w", err)
        }</span>

        <span class="cov6" title="15">if err = DB.Ping(); err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("failed to ping database: %w", err)
        }</span>

        <span class="cov6" title="15">if err = createTables(); err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("failed to create tables: %w", err)
        }</span>

        <span class="cov6" title="15">log.Println("Database initialized successfully")
        return nil</span>
}

func createTables() error <span class="cov6" title="15">{
        // Users table
        usersTable := `
        CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                email TEXT UNIQUE NOT NULL,
                password TEXT NOT NULL,
                role TEXT NOT NULL DEFAULT 'user',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        );`

        // Products table
        productsTable := `
        CREATE TABLE IF NOT EXISTS products (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                description TEXT NOT NULL,
                price REAL NOT NULL,
                stock INTEGER NOT NULL DEFAULT 0,
                created_by INTEGER NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (created_by) REFERENCES users(id)
        );`

        // Orders table
        ordersTable := `
        CREATE TABLE IF NOT EXISTS orders (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                product_id INTEGER NOT NULL,
                quantity INTEGER NOT NULL,
                price REAL NOT NULL,
                total REAL NOT NULL,
                status TEXT NOT NULL DEFAULT 'pending',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id),
                FOREIGN KEY (product_id) REFERENCES products(id)
        );`

        // Chat messages table
        chatTable := `
        CREATE TABLE IF NOT EXISTS chat_messages (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                username TEXT NOT NULL,
                message TEXT NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id)
        );`

        tables := []string{usersTable, productsTable, ordersTable, chatTable}

        for _, table := range tables </span><span class="cov10" title="60">{
                if _, err := DB.Exec(table); err != nil </span><span class="cov0" title="0">{
                        return fmt.Errorf("failed to create table: %w", err)
                }</span>
        }

        <span class="cov6" title="15">return nil</span>
}

func CloseDB() error <span class="cov6" title="15">{
        if DB != nil </span><span class="cov6" title="15">{
                return DB.Close()
        }</span>
        <span class="cov0" title="0">return nil</span>
}
</pre>
		
		<pre class="file" id="file3" style="display: none">// Package docs Code generated by swaggo/swag. DO NOT EDIT
package docs

import "github.com/swaggo/swag"

const docTemplate = `{
    "schemes": {{ marshal .Schemes }},
    "swagger": "2.0",
    "info": {
        "description": "{{escape .Description}}",
        "title": "{{.Title}}",
        "termsOfService": "http://swagger.io/terms/",
        "contact": {
            "name": "API Support",
            "url": "http://www.swagger.io/support",
            "email": "<EMAIL>"
        },
        "license": {
            "name": "MIT",
            "url": "https://opensource.org/licenses/MIT"
        },
        "version": "{{.Version}}"
    },
    "host": "{{.Host}}",
    "basePath": "{{.BasePath}}",
    "paths": {
        "/auth/login": {
            "post": {
                "description": "Authenticate user with email and password",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Authentication"
                ],
                "summary": "Login user",
                "parameters": [
                    {
                        "description": "User login credentials",
                        "name": "credentials",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.LoginRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/models.LoginResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    }
                }
            }
        },
        "/auth/register": {
            "post": {
                "description": "Register a new user with username, email, password and optional role",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Authentication"
                ],
                "summary": "Register a new user",
                "parameters": [
                    {
                        "description": "User registration data",
                        "name": "user",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.RegisterRequest"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Created",
                        "schema": {
                            "$ref": "#/definitions/models.LoginResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "409": {
                        "description": "Conflict",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    }
                }
            }
        },
        "/orders": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Create a new order to purchase a product, automatically reduces stock",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Orders"
                ],
                "summary": "Create a new order (Buy a product)",
                "parameters": [
                    {
                        "description": "Order data",
                        "name": "order",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.CreateOrderRequest"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Created",
                        "schema": {
                            "$ref": "#/definitions/models.OrderResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    }
                }
            }
        },
        "/products": {
            "get": {
                "description": "Get a list of all products",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Products"
                ],
                "summary": "Get all products",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/models.Product"
                            }
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Create a new product with name, description, price and stock",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Products"
                ],
                "summary": "Create a new product (Admin only)",
                "parameters": [
                    {
                        "description": "Product data",
                        "name": "product",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.CreateProductRequest"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Created",
                        "schema": {
                            "$ref": "#/definitions/models.Product"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "403": {
                        "description": "Forbidden",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    }
                }
            }
        },
        "/profile": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Get the profile of the authenticated user",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Authentication"
                ],
                "summary": "Get user profile",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/models.User"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    }
                }
            }
        }
    },
    "definitions": {
        "models.CreateOrderRequest": {
            "type": "object",
            "required": [
                "product_id",
                "quantity"
            ],
            "properties": {
                "product_id": {
                    "type": "integer"
                },
                "quantity": {
                    "type": "integer"
                }
            }
        },
        "models.CreateProductRequest": {
            "type": "object",
            "required": [
                "description",
                "name",
                "price",
                "stock"
            ],
            "properties": {
                "description": {
                    "type": "string",
                    "maxLength": 500,
                    "minLength": 1
                },
                "name": {
                    "type": "string",
                    "maxLength": 100,
                    "minLength": 1
                },
                "price": {
                    "type": "number"
                },
                "stock": {
                    "type": "integer",
                    "minimum": 0
                }
            }
        },
        "models.LoginRequest": {
            "type": "object",
            "required": [
                "email",
                "password"
            ],
            "properties": {
                "email": {
                    "type": "string"
                },
                "password": {
                    "type": "string",
                    "minLength": 6
                }
            }
        },
        "models.LoginResponse": {
            "type": "object",
            "properties": {
                "token": {
                    "type": "string"
                },
                "user": {
                    "$ref": "#/definitions/models.User"
                }
            }
        },
        "models.Order": {
            "type": "object",
            "properties": {
                "created_at": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "price": {
                    "description": "Price at time of purchase",
                    "type": "number"
                },
                "product_id": {
                    "type": "integer"
                },
                "quantity": {
                    "type": "integer"
                },
                "status": {
                    "$ref": "#/definitions/models.OrderStatus"
                },
                "total": {
                    "type": "number"
                },
                "updated_at": {
                    "type": "string"
                },
                "user_id": {
                    "type": "integer"
                }
            }
        },
        "models.OrderResponse": {
            "type": "object",
            "properties": {
                "order": {
                    "$ref": "#/definitions/models.Order"
                },
                "product": {
                    "$ref": "#/definitions/models.Product"
                }
            }
        },
        "models.OrderStatus": {
            "type": "string",
            "enum": [
                "pending",
                "completed",
                "cancelled"
            ],
            "x-enum-varnames": [
                "OrderStatusPending",
                "OrderStatusCompleted",
                "OrderStatusCancelled"
            ]
        },
        "models.Product": {
            "type": "object",
            "properties": {
                "created_at": {
                    "type": "string"
                },
                "created_by": {
                    "type": "integer"
                },
                "description": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "name": {
                    "type": "string"
                },
                "price": {
                    "type": "number"
                },
                "stock": {
                    "type": "integer"
                },
                "updated_at": {
                    "type": "string"
                }
            }
        },
        "models.RegisterRequest": {
            "type": "object",
            "required": [
                "email",
                "password",
                "username"
            ],
            "properties": {
                "email": {
                    "type": "string"
                },
                "password": {
                    "type": "string",
                    "minLength": 6
                },
                "role": {
                    "description": "Optional, defaults to user",
                    "allOf": [
                        {
                            "$ref": "#/definitions/models.Role"
                        }
                    ]
                },
                "username": {
                    "type": "string",
                    "maxLength": 50,
                    "minLength": 3
                }
            }
        },
        "models.Role": {
            "type": "string",
            "enum": [
                "admin",
                "user"
            ],
            "x-enum-varnames": [
                "RoleAdmin",
                "RoleUser"
            ]
        },
        "models.User": {
            "type": "object",
            "properties": {
                "created_at": {
                    "type": "string"
                },
                "email": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "role": {
                    "$ref": "#/definitions/models.Role"
                },
                "updated_at": {
                    "type": "string"
                },
                "username": {
                    "type": "string"
                }
            }
        }
    },
    "securityDefinitions": {
        "BearerAuth": {
            "description": "Type \"Bearer\" followed by a space and JWT token.",
            "type": "apiKey",
            "name": "Authorization",
            "in": "header"
        }
    }
}`

// SwaggerInfo holds exported Swagger Info so clients can modify it
var SwaggerInfo = &amp;swag.Spec{
        Version:          "1.0",
        Host:             "127.0.0.1:8081",
        BasePath:         "/api/v1",
        Schemes:          []string{"http", "https"},
        Title:            "SmarApp API",
        Description:      "A simple Go API with JWT authentication, role-based access control, WebSocket chat, and product management.",
        InfoInstanceName: "swagger",
        SwaggerTemplate:  docTemplate,
        LeftDelim:        "{{",
        RightDelim:       "}}",
}

func init() <span class="cov0" title="0">{
        swag.Register(SwaggerInfo.InstanceName(), SwaggerInfo)
}</span>
</pre>
		
		<pre class="file" id="file4" style="display: none">package handlers

import (
        "database/sql"
        "net/http"
        "smarapp-api/database"
        "smarapp-api/middleware"
        "smarapp-api/models"
        "time"

        "github.com/gin-gonic/gin"
        "golang.org/x/crypto/bcrypt"
)

type AuthHandler struct {
        JWTSecret string
}

func NewAuthHandler(jwtSecret string) *AuthHandler <span class="cov5" title="3">{
        return &amp;AuthHandler{
                JWTSecret: jwtSecret,
        }
}</span>

// Register godoc
// @Summary Register a new user
// @Description Register a new user with username, email, password and optional role
// @Tags Authentication
// @Accept json
// @Produce json
// @Param user body models.RegisterRequest true "User registration data"
// @Success 201 {object} models.LoginResponse
// @Failure 400 {object} map[string]string
// @Failure 409 {object} map[string]string
// @Failure 500 {object} map[string]string
// @Router /auth/register [post]
func (h *AuthHandler) Register(c *gin.Context) <span class="cov10" title="9">{
        var req models.RegisterRequest
        if err := c.ShouldBindJSON(&amp;req); err != nil </span><span class="cov3" title="2">{
                c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
                return
        }</span>

        // Set default role if not provided
        <span class="cov8" title="7">if req.Role == "" </span><span class="cov5" title="3">{
                req.Role = models.RoleUser
        }</span>

        // Validate role
        <span class="cov8" title="7">if req.Role != models.RoleUser &amp;&amp; req.Role != models.RoleAdmin </span><span class="cov1" title="1">{
                c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid role"})
                return
        }</span>

        // Hash password
        <span class="cov8" title="6">hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.Password), bcrypt.DefaultCost)
        if err != nil </span><span class="cov0" title="0">{
                c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to hash password"})
                return
        }</span>

        // Check if user already exists
        <span class="cov8" title="6">var existingID int
        err = database.DB.QueryRow("SELECT id FROM users WHERE email = ? OR username = ?", req.Email, req.Username).Scan(&amp;existingID)
        if err != sql.ErrNoRows </span><span class="cov3" title="2">{
                c.JSON(http.StatusConflict, gin.H{"error": "User with this email or username already exists"})
                return
        }</span>

        // Insert user
        <span class="cov6" title="4">result, err := database.DB.Exec(
                "INSERT INTO users (username, email, password, role, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?)",
                req.Username, req.Email, string(hashedPassword), req.Role, time.Now(), time.Now(),
        )
        if err != nil </span><span class="cov0" title="0">{
                c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create user"})
                return
        }</span>

        <span class="cov6" title="4">userID, _ := result.LastInsertId()

        user := models.User{
                ID:        int(userID),
                Username:  req.Username,
                Email:     req.Email,
                Role:      req.Role,
                CreatedAt: time.Now(),
                UpdatedAt: time.Now(),
        }

        // Generate token
        token, err := middleware.GenerateToken(user, h.JWTSecret)
        if err != nil </span><span class="cov0" title="0">{
                c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate token"})
                return
        }</span>

        <span class="cov6" title="4">c.JSON(http.StatusCreated, models.LoginResponse{
                Token: token,
                User:  user,
        })</span>
}

// Login godoc
// @Summary Login user
// @Description Authenticate user with email and password
// @Tags Authentication
// @Accept json
// @Produce json
// @Param credentials body models.LoginRequest true "User login credentials"
// @Success 200 {object} models.LoginResponse
// @Failure 400 {object} map[string]string
// @Failure 401 {object} map[string]string
// @Failure 500 {object} map[string]string
// @Router /auth/login [post]
func (h *AuthHandler) Login(c *gin.Context) <span class="cov6" title="4">{
        var req models.LoginRequest
        if err := c.ShouldBindJSON(&amp;req); err != nil </span><span class="cov1" title="1">{
                c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
                return
        }</span>

        <span class="cov5" title="3">var user models.User
        var hashedPassword string
        err := database.DB.QueryRow(
                "SELECT id, username, email, password, role, created_at, updated_at FROM users WHERE email = ?",
                req.Email,
        ).Scan(&amp;user.ID, &amp;user.Username, &amp;user.Email, &amp;hashedPassword, &amp;user.Role, &amp;user.CreatedAt, &amp;user.UpdatedAt)

        if err == sql.ErrNoRows </span><span class="cov1" title="1">{
                c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid credentials"})
                return
        }</span>
        <span class="cov3" title="2">if err != nil </span><span class="cov0" title="0">{
                c.JSON(http.StatusInternalServerError, gin.H{"error": "Database error"})
                return
        }</span>

        // Check password
        <span class="cov3" title="2">if err := bcrypt.CompareHashAndPassword([]byte(hashedPassword), []byte(req.Password)); err != nil </span><span class="cov1" title="1">{
                c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid credentials"})
                return
        }</span>

        // Generate token
        <span class="cov1" title="1">token, err := middleware.GenerateToken(user, h.JWTSecret)
        if err != nil </span><span class="cov0" title="0">{
                c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate token"})
                return
        }</span>

        <span class="cov1" title="1">c.JSON(http.StatusOK, models.LoginResponse{
                Token: token,
                User:  user,
        })</span>
}

// GetProfile godoc
// @Summary Get user profile
// @Description Get the profile of the authenticated user
// @Tags Authentication
// @Produce json
// @Security BearerAuth
// @Success 200 {object} models.User
// @Failure 401 {object} map[string]string
// @Failure 404 {object} map[string]string
// @Router /profile [get]
func (h *AuthHandler) GetProfile(c *gin.Context) <span class="cov0" title="0">{
        userID, _ := c.Get("user_id")
        
        var user models.User
        err := database.DB.QueryRow(
                "SELECT id, username, email, role, created_at, updated_at FROM users WHERE id = ?",
                userID,
        ).Scan(&amp;user.ID, &amp;user.Username, &amp;user.Email, &amp;user.Role, &amp;user.CreatedAt, &amp;user.UpdatedAt)

        if err != nil </span><span class="cov0" title="0">{
                c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
                return
        }</span>

        <span class="cov0" title="0">c.JSON(http.StatusOK, user)</span>
}
</pre>
		
		<pre class="file" id="file5" style="display: none">package handlers

import (
        "net/http"
        "smarapp-api/database"
        "smarapp-api/models"
        "smarapp-api/websocket"

        "github.com/gin-gonic/gin"
)

type ChatHandler struct {
        Hub *websocket.Hub
}

func NewChatHandler(hub *websocket.Hub) *ChatHandler <span class="cov10" title="2">{
        return &amp;ChatHandler{
                Hub: hub,
        }
}</span>

func (h *ChatHandler) HandleWebSocket(c *gin.Context) <span class="cov10" title="2">{
        userID, exists := c.Get("user_id")
        if !exists </span><span class="cov1" title="1">{
                c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
                return
        }</span>

        <span class="cov1" title="1">username, exists := c.Get("username")
        if !exists </span><span class="cov1" title="1">{
                c.JSON(http.StatusUnauthorized, gin.H{"error": "Username not found"})
                return
        }</span>

        <span class="cov0" title="0">websocket.ServeWS(h.Hub, c.Writer, c.Request, userID.(int), username.(string))</span>
}

func (h *ChatHandler) GetChatHistory(c *gin.Context) <span class="cov1" title="1">{
        rows, err := database.DB.Query(`
                SELECT id, user_id, username, message, created_at 
                FROM chat_messages 
                ORDER BY created_at DESC 
                LIMIT 100
        `)
        if err != nil </span><span class="cov0" title="0">{
                c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch chat history"})
                return
        }</span>
        <span class="cov1" title="1">defer rows.Close()

        var messages []models.ChatMessage
        for rows.Next() </span><span class="cov1" title="1">{
                var msg models.ChatMessage
                err := rows.Scan(&amp;msg.ID, &amp;msg.UserID, &amp;msg.Username, &amp;msg.Message, &amp;msg.CreatedAt)
                if err != nil </span><span class="cov0" title="0">{
                        c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to scan message"})
                        return
                }</span>
                <span class="cov1" title="1">messages = append(messages, msg)</span>
        }

        // Reverse to show oldest first
        <span class="cov1" title="1">for i := len(messages)/2 - 1; i &gt;= 0; i-- </span><span class="cov0" title="0">{
                opp := len(messages) - 1 - i
                messages[i], messages[opp] = messages[opp], messages[i]
        }</span>

        <span class="cov1" title="1">c.JSON(http.StatusOK, messages)</span>
}
</pre>
		
		<pre class="file" id="file6" style="display: none">package handlers

import (
        "database/sql"
        "net/http"
        "smarapp-api/database"
        "smarapp-api/models"
        "strconv"
        "time"

        "github.com/gin-gonic/gin"
)

type OrderHandler struct{}

func NewOrderHandler() *OrderHandler <span class="cov10" title="5">{
        return &amp;OrderHandler{}
}</span>

// CreateOrder godoc
// @Summary Create a new order (Buy a product)
// @Description Create a new order to purchase a product, automatically reduces stock
// @Tags Orders
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param order body models.CreateOrderRequest true "Order data"
// @Success 201 {object} models.OrderResponse
// @Failure 400 {object} map[string]string
// @Failure 401 {object} map[string]string
// @Failure 404 {object} map[string]string
// @Failure 500 {object} map[string]string
// @Router /orders [post]
func (h *OrderHandler) CreateOrder(c *gin.Context) <span class="cov10" title="5">{
        var req models.CreateOrderRequest
        if err := c.ShouldBindJSON(&amp;req); err != nil </span><span class="cov1" title="1">{
                c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
                return
        }</span>

        <span class="cov8" title="4">userID, _ := c.Get("user_id")

        // Start transaction
        tx, err := database.DB.Begin()
        if err != nil </span><span class="cov0" title="0">{
                c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to start transaction"})
                return
        }</span>
        <span class="cov8" title="4">defer tx.Rollback()

        // Get product and check stock
        var product models.Product
        err = tx.QueryRow(
                "SELECT id, name, description, price, stock, created_by, created_at, updated_at FROM products WHERE id = ?",
                req.ProductID,
        ).Scan(
                &amp;product.ID, &amp;product.Name, &amp;product.Description, &amp;product.Price,
                &amp;product.Stock, &amp;product.CreatedBy, &amp;product.CreatedAt, &amp;product.UpdatedAt,
        )

        if err == sql.ErrNoRows </span><span class="cov1" title="1">{
                c.JSON(http.StatusNotFound, gin.H{"error": "Product not found"})
                return
        }</span>
        <span class="cov7" title="3">if err != nil </span><span class="cov0" title="0">{
                c.JSON(http.StatusInternalServerError, gin.H{"error": "Database error"})
                return
        }</span>

        // Check if enough stock
        <span class="cov7" title="3">if product.Stock &lt; req.Quantity </span><span class="cov1" title="1">{
                c.JSON(http.StatusBadRequest, gin.H{"error": "Insufficient stock"})
                return
        }</span>

        // Calculate total
        <span class="cov4" title="2">total := product.Price * float64(req.Quantity)

        // Create order
        result, err := tx.Exec(
                "INSERT INTO orders (user_id, product_id, quantity, price, total, status, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?)",
                userID, req.ProductID, req.Quantity, product.Price, total, models.OrderStatusPending, time.Now(), time.Now(),
        )
        if err != nil </span><span class="cov0" title="0">{
                c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create order"})
                return
        }</span>

        <span class="cov4" title="2">orderID, _ := result.LastInsertId()

        // Update product stock
        _, err = tx.Exec(
                "UPDATE products SET stock = stock - ?, updated_at = ? WHERE id = ?",
                req.Quantity, time.Now(), req.ProductID,
        )
        if err != nil </span><span class="cov0" title="0">{
                c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update stock"})
                return
        }</span>

        // Complete order (simulate payment success)
        <span class="cov4" title="2">_, err = tx.Exec(
                "UPDATE orders SET status = ?, updated_at = ? WHERE id = ?",
                models.OrderStatusCompleted, time.Now(), orderID,
        )
        if err != nil </span><span class="cov0" title="0">{
                c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to complete order"})
                return
        }</span>

        // Commit transaction
        <span class="cov4" title="2">if err = tx.Commit(); err != nil </span><span class="cov0" title="0">{
                c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to commit transaction"})
                return
        }</span>

        <span class="cov4" title="2">order := models.Order{
                ID:        int(orderID),
                UserID:    userID.(int),
                ProductID: req.ProductID,
                Quantity:  req.Quantity,
                Price:     product.Price,
                Total:     total,
                Status:    models.OrderStatusCompleted,
                CreatedAt: time.Now(),
                UpdatedAt: time.Now(),
        }

        // Update product stock for response
        product.Stock -= req.Quantity

        c.JSON(http.StatusCreated, models.OrderResponse{
                Order:   order,
                Product: product,
        })</span>
}

func (h *OrderHandler) GetUserOrders(c *gin.Context) <span class="cov1" title="1">{
        userID, _ := c.Get("user_id")

        rows, err := database.DB.Query(`
                SELECT o.id, o.user_id, o.product_id, o.quantity, o.price, o.total, o.status, o.created_at, o.updated_at,
                       p.name as product_name
                FROM orders o
                JOIN products p ON o.product_id = p.id
                WHERE o.user_id = ?
                ORDER BY o.created_at DESC
        `, userID)

        if err != nil </span><span class="cov0" title="0">{
                c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch orders"})
                return
        }</span>
        <span class="cov1" title="1">defer rows.Close()

        var orders []models.OrderWithDetails
        for rows.Next() </span><span class="cov1" title="1">{
                var order models.OrderWithDetails
                err := rows.Scan(
                        &amp;order.ID, &amp;order.UserID, &amp;order.ProductID, &amp;order.Quantity,
                        &amp;order.Price, &amp;order.Total, &amp;order.Status, &amp;order.CreatedAt, &amp;order.UpdatedAt,
                        &amp;order.ProductName,
                )
                if err != nil </span><span class="cov0" title="0">{
                        c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to scan order"})
                        return
                }</span>
                <span class="cov1" title="1">orders = append(orders, order)</span>
        }

        <span class="cov1" title="1">c.JSON(http.StatusOK, orders)</span>
}

func (h *OrderHandler) GetAllOrders(c *gin.Context) <span class="cov1" title="1">{
        rows, err := database.DB.Query(`
                SELECT o.id, o.user_id, o.product_id, o.quantity, o.price, o.total, o.status, o.created_at, o.updated_at,
                       p.name as product_name, u.username
                FROM orders o
                JOIN products p ON o.product_id = p.id
                JOIN users u ON o.user_id = u.id
                ORDER BY o.created_at DESC
        `)

        if err != nil </span><span class="cov0" title="0">{
                c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch orders"})
                return
        }</span>
        <span class="cov1" title="1">defer rows.Close()

        var orders []models.OrderWithDetails
        for rows.Next() </span><span class="cov1" title="1">{
                var order models.OrderWithDetails
                err := rows.Scan(
                        &amp;order.ID, &amp;order.UserID, &amp;order.ProductID, &amp;order.Quantity,
                        &amp;order.Price, &amp;order.Total, &amp;order.Status, &amp;order.CreatedAt, &amp;order.UpdatedAt,
                        &amp;order.ProductName, &amp;order.Username,
                )
                if err != nil </span><span class="cov0" title="0">{
                        c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to scan order"})
                        return
                }</span>
                <span class="cov1" title="1">orders = append(orders, order)</span>
        }

        <span class="cov1" title="1">c.JSON(http.StatusOK, orders)</span>
}

func (h *OrderHandler) GetOrder(c *gin.Context) <span class="cov10" title="5">{
        idParam := c.Param("id")
        id, err := strconv.Atoi(idParam)
        if err != nil </span><span class="cov1" title="1">{
                c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid order ID"})
                return
        }</span>

        <span class="cov8" title="4">userID, _ := c.Get("user_id")
        role, _ := c.Get("role")

        query := `
                SELECT o.id, o.user_id, o.product_id, o.quantity, o.price, o.total, o.status, o.created_at, o.updated_at,
                       p.name as product_name
                FROM orders o
                JOIN products p ON o.product_id = p.id
                WHERE o.id = ?`

        args := []interface{}{id}

        // If not admin, only show user's own orders
        if role != models.RoleAdmin </span><span class="cov7" title="3">{
                query += " AND o.user_id = ?"
                args = append(args, userID)
        }</span>

        <span class="cov8" title="4">var order models.OrderWithDetails
        err = database.DB.QueryRow(query, args...).Scan(
                &amp;order.ID, &amp;order.UserID, &amp;order.ProductID, &amp;order.Quantity,
                &amp;order.Price, &amp;order.Total, &amp;order.Status, &amp;order.CreatedAt, &amp;order.UpdatedAt,
                &amp;order.ProductName,
        )

        if err == sql.ErrNoRows </span><span class="cov4" title="2">{
                c.JSON(http.StatusNotFound, gin.H{"error": "Order not found"})
                return
        }</span>
        <span class="cov4" title="2">if err != nil </span><span class="cov0" title="0">{
                c.JSON(http.StatusInternalServerError, gin.H{"error": "Database error"})
                return
        }</span>

        <span class="cov4" title="2">c.JSON(http.StatusOK, order)</span>
}
</pre>
		
		<pre class="file" id="file7" style="display: none">package handlers

import (
        "database/sql"
        "net/http"
        "smarapp-api/database"
        "smarapp-api/models"
        "strconv"
        "time"

        "github.com/gin-gonic/gin"
)

type ProductHandler struct{}

func NewProductHandler() *ProductHandler <span class="cov10" title="5">{
        return &amp;ProductHandler{}
}</span>

// CreateProduct godoc
// @Summary Create a new product (Admin only)
// @Description Create a new product with name, description, price and stock
// @Tags Products
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param product body models.CreateProductRequest true "Product data"
// @Success 201 {object} models.Product
// @Failure 400 {object} map[string]string
// @Failure 401 {object} map[string]string
// @Failure 403 {object} map[string]string
// @Failure 500 {object} map[string]string
// @Router /products [post]
func (h *ProductHandler) CreateProduct(c *gin.Context) <span class="cov8" title="4">{
        var req models.CreateProductRequest
        if err := c.ShouldBindJSON(&amp;req); err != nil </span><span class="cov7" title="3">{
                c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
                return
        }</span>

        <span class="cov1" title="1">userID, _ := c.Get("user_id")

        result, err := database.DB.Exec(
                "INSERT INTO products (name, description, price, stock, created_by, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?)",
                req.Name, req.Description, req.Price, req.Stock, userID, time.Now(), time.Now(),
        )
        if err != nil </span><span class="cov0" title="0">{
                c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create product"})
                return
        }</span>

        <span class="cov1" title="1">productID, _ := result.LastInsertId()

        product := models.Product{
                ID:          int(productID),
                Name:        req.Name,
                Description: req.Description,
                Price:       req.Price,
                Stock:       req.Stock,
                CreatedBy:   userID.(int),
                CreatedAt:   time.Now(),
                UpdatedAt:   time.Now(),
        }

        c.JSON(http.StatusCreated, product)</span>
}

// GetProducts godoc
// @Summary Get all products
// @Description Get a list of all products
// @Tags Products
// @Produce json
// @Success 200 {array} models.Product
// @Failure 500 {object} map[string]string
// @Router /products [get]
func (h *ProductHandler) GetProducts(c *gin.Context) <span class="cov1" title="1">{
        rows, err := database.DB.Query(
                "SELECT id, name, description, price, stock, created_by, created_at, updated_at FROM products ORDER BY created_at DESC",
        )
        if err != nil </span><span class="cov0" title="0">{
                c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch products"})
                return
        }</span>
        <span class="cov1" title="1">defer rows.Close()

        var products []models.Product
        for rows.Next() </span><span class="cov4" title="2">{
                var product models.Product
                err := rows.Scan(
                        &amp;product.ID, &amp;product.Name, &amp;product.Description, &amp;product.Price,
                        &amp;product.Stock, &amp;product.CreatedBy, &amp;product.CreatedAt, &amp;product.UpdatedAt,
                )
                if err != nil </span><span class="cov0" title="0">{
                        c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to scan product"})
                        return
                }</span>
                <span class="cov4" title="2">products = append(products, product)</span>
        }

        <span class="cov1" title="1">c.JSON(http.StatusOK, products)</span>
}

func (h *ProductHandler) GetProduct(c *gin.Context) <span class="cov10" title="5">{
        idParam := c.Param("id")
        id, err := strconv.Atoi(idParam)
        if err != nil </span><span class="cov1" title="1">{
                c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid product ID"})
                return
        }</span>

        <span class="cov8" title="4">var product models.Product
        err = database.DB.QueryRow(
                "SELECT id, name, description, price, stock, created_by, created_at, updated_at FROM products WHERE id = ?",
                id,
        ).Scan(
                &amp;product.ID, &amp;product.Name, &amp;product.Description, &amp;product.Price,
                &amp;product.Stock, &amp;product.CreatedBy, &amp;product.CreatedAt, &amp;product.UpdatedAt,
        )

        if err == sql.ErrNoRows </span><span class="cov1" title="1">{
                c.JSON(http.StatusNotFound, gin.H{"error": "Product not found"})
                return
        }</span>
        <span class="cov7" title="3">if err != nil </span><span class="cov0" title="0">{
                c.JSON(http.StatusInternalServerError, gin.H{"error": "Database error"})
                return
        }</span>

        <span class="cov7" title="3">c.JSON(http.StatusOK, product)</span>
}

func (h *ProductHandler) UpdateProduct(c *gin.Context) <span class="cov8" title="4">{
        idParam := c.Param("id")
        id, err := strconv.Atoi(idParam)
        if err != nil </span><span class="cov1" title="1">{
                c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid product ID"})
                return
        }</span>

        <span class="cov7" title="3">var req models.UpdateProductRequest
        if err := c.ShouldBindJSON(&amp;req); err != nil </span><span class="cov0" title="0">{
                c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
                return
        }</span>

        // Check if product exists
        <span class="cov7" title="3">var existingProduct models.Product
        err = database.DB.QueryRow("SELECT id FROM products WHERE id = ?", id).Scan(&amp;existingProduct.ID)
        if err == sql.ErrNoRows </span><span class="cov1" title="1">{
                c.JSON(http.StatusNotFound, gin.H{"error": "Product not found"})
                return
        }</span>

        // Build dynamic update query
        <span class="cov4" title="2">query := "UPDATE products SET updated_at = ?"
        args := []interface{}{time.Now()}

        if req.Name != "" </span><span class="cov1" title="1">{
                query += ", name = ?"
                args = append(args, req.Name)
        }</span>
        <span class="cov4" title="2">if req.Description != "" </span><span class="cov0" title="0">{
                query += ", description = ?"
                args = append(args, req.Description)
        }</span>
        <span class="cov4" title="2">if req.Price &gt; 0 </span><span class="cov1" title="1">{
                query += ", price = ?"
                args = append(args, req.Price)
        }</span>
        <span class="cov4" title="2">if req.Stock &gt;= 0 </span><span class="cov4" title="2">{
                query += ", stock = ?"
                args = append(args, req.Stock)
        }</span>

        <span class="cov4" title="2">query += " WHERE id = ?"
        args = append(args, id)

        _, err = database.DB.Exec(query, args...)
        if err != nil </span><span class="cov0" title="0">{
                c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update product"})
                return
        }</span>

        // Fetch updated product
        <span class="cov4" title="2">h.GetProduct(c)</span>
}

func (h *ProductHandler) DeleteProduct(c *gin.Context) <span class="cov7" title="3">{
        idParam := c.Param("id")
        id, err := strconv.Atoi(idParam)
        if err != nil </span><span class="cov1" title="1">{
                c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid product ID"})
                return
        }</span>

        <span class="cov4" title="2">result, err := database.DB.Exec("DELETE FROM products WHERE id = ?", id)
        if err != nil </span><span class="cov0" title="0">{
                c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete product"})
                return
        }</span>

        <span class="cov4" title="2">rowsAffected, _ := result.RowsAffected()
        if rowsAffected == 0 </span><span class="cov1" title="1">{
                c.JSON(http.StatusNotFound, gin.H{"error": "Product not found"})
                return
        }</span>

        <span class="cov1" title="1">c.JSON(http.StatusOK, gin.H{"message": "Product deleted successfully"})</span>
}
</pre>
		
		<pre class="file" id="file8" style="display: none">package middleware

import (
        "net/http"
        "smarapp-api/models"
        "strings"
        "time"

        "github.com/gin-gonic/gin"
        "github.com/golang-jwt/jwt/v5"
)

type Claims struct {
        UserID   int         `json:"user_id"`
        Username string      `json:"username"`
        Email    string      `json:"email"`
        Role     models.Role `json:"role"`
        jwt.RegisteredClaims
}

func GenerateToken(user models.User, jwtSecret string) (string, error) <span class="cov5" title="2">{
        claims := Claims{
                UserID:   user.ID,
                Username: user.Username,
                Email:    user.Email,
                Role:     user.Role,
                RegisteredClaims: jwt.RegisteredClaims{
                        ExpiresAt: jwt.NewNumericDate(time.Now().Add(24 * time.Hour)),
                        IssuedAt:  jwt.NewNumericDate(time.Now()),
                },
        }

        token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
        return token.SignedString([]byte(jwtSecret))
}</span>

func AuthMiddleware(jwtSecret string) gin.HandlerFunc <span class="cov10" title="4">{
        return func(c *gin.Context) </span><span class="cov10" title="4">{
                authHeader := c.GetHeader("Authorization")
                if authHeader == "" </span><span class="cov1" title="1">{
                        c.JSON(http.StatusUnauthorized, gin.H{"error": "Authorization header required"})
                        c.Abort()
                        return
                }</span>

                <span class="cov8" title="3">tokenString := strings.TrimPrefix(authHeader, "Bearer ")
                if tokenString == authHeader </span><span class="cov1" title="1">{
                        c.JSON(http.StatusUnauthorized, gin.H{"error": "Bearer token required"})
                        c.Abort()
                        return
                }</span>

                <span class="cov5" title="2">claims := &amp;Claims{}
                token, err := jwt.ParseWithClaims(tokenString, claims, func(token *jwt.Token) (interface{}, error) </span><span class="cov1" title="1">{
                        return []byte(jwtSecret), nil
                }</span>)

                <span class="cov5" title="2">if err != nil || !token.Valid </span><span class="cov1" title="1">{
                        c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid token"})
                        c.Abort()
                        return
                }</span>

                // Set user information in context
                <span class="cov1" title="1">c.Set("user_id", claims.UserID)
                c.Set("username", claims.Username)
                c.Set("email", claims.Email)
                c.Set("role", claims.Role)

                c.Next()</span>
        }
}

func AdminMiddleware() gin.HandlerFunc <span class="cov8" title="3">{
        return func(c *gin.Context) </span><span class="cov8" title="3">{
                role, exists := c.Get("role")
                if !exists </span><span class="cov1" title="1">{
                        c.JSON(http.StatusUnauthorized, gin.H{"error": "User role not found"})
                        c.Abort()
                        return
                }</span>

                <span class="cov5" title="2">if role != models.RoleAdmin </span><span class="cov1" title="1">{
                        c.JSON(http.StatusForbidden, gin.H{"error": "Admin access required"})
                        c.Abort()
                        return
                }</span>

                <span class="cov1" title="1">c.Next()</span>
        }
}
</pre>
		
		<pre class="file" id="file9" style="display: none">package testutil

import (
        "fmt"
        "os"
        "path/filepath"
        "smarapp-api/database"
        "testing"

        _ "github.com/mattn/go-sqlite3"
)

// SetupTestDB creates a temporary test database and returns a cleanup function
func SetupTestDB(t *testing.T) func() <span class="cov10" title="15">{
        // Create a temporary database file
        tempDir := t.TempDir()
        dbPath := filepath.Join(tempDir, "test.db")

        // Initialize the test database
        err := database.InitDB(dbPath)
        if err != nil </span><span class="cov0" title="0">{
                t.Fatalf("Failed to initialize test database: %v", err)
        }</span>

        // Return cleanup function
        <span class="cov10" title="15">return func() </span><span class="cov10" title="15">{
                database.CloseDB()
                os.Remove(dbPath)
        }</span>
}

// SetupTestDBWithData creates a test database and populates it with test data
func SetupTestDBWithData(t *testing.T) func() <span class="cov8" title="11">{
        cleanup := SetupTestDB(t)

        // Insert test users
        _, err := database.DB.Exec(`
                INSERT INTO users (id, username, email, password, role, created_at, updated_at) 
                VALUES 
                (1, 'admin', '<EMAIL>', '$2a$10$test.hash.admin', 'admin', datetime('now'), datetime('now')),
                (2, 'user', '<EMAIL>', '$2a$10$test.hash.user', 'user', datetime('now'), datetime('now'))
        `)
        if err != nil </span><span class="cov0" title="0">{
                t.Fatalf("Failed to insert test users: %v", err)
        }</span>

        // Insert test products
        <span class="cov8" title="11">_, err = database.DB.Exec(`
                INSERT INTO products (id, name, description, price, stock, created_by, created_at, updated_at)
                VALUES 
                (1, 'Test Product 1', 'Test Description 1', 99.99, 10, 1, datetime('now'), datetime('now')),
                (2, 'Test Product 2', 'Test Description 2', 149.99, 5, 1, datetime('now'), datetime('now'))
        `)
        if err != nil </span><span class="cov0" title="0">{
                t.Fatalf("Failed to insert test products: %v", err)
        }</span>

        // Insert test orders
        <span class="cov8" title="11">_, err = database.DB.Exec(`
                INSERT INTO orders (id, user_id, product_id, quantity, price, total, status, created_at, updated_at)
                VALUES 
                (1, 2, 1, 2, 99.99, 199.98, 'completed', datetime('now'), datetime('now'))
        `)
        if err != nil </span><span class="cov0" title="0">{
                t.Fatalf("Failed to insert test orders: %v", err)
        }</span>

        // Insert test chat messages
        <span class="cov8" title="11">_, err = database.DB.Exec(`
                INSERT INTO chat_messages (id, user_id, username, message, created_at)
                VALUES 
                (1, 2, 'user', 'Hello, world!', datetime('now'))
        `)
        if err != nil </span><span class="cov0" title="0">{
                t.Fatalf("Failed to insert test chat messages: %v", err)
        }</span>

        <span class="cov8" title="11">return cleanup</span>
}

// GetTestUser returns a test user by ID
func GetTestUser(t *testing.T, userID int) (map[string]interface{}, error) <span class="cov0" title="0">{
        var id int
        var username, email, role string
        var createdAt, updatedAt string

        err := database.DB.QueryRow(
                "SELECT id, username, email, role, created_at, updated_at FROM users WHERE id = ?",
                userID,
        ).Scan(&amp;id, &amp;username, &amp;email, &amp;role, &amp;createdAt, &amp;updatedAt)

        if err != nil </span><span class="cov0" title="0">{
                return nil, err
        }</span>

        <span class="cov0" title="0">return map[string]interface{}{
                "id":         id,
                "username":   username,
                "email":      email,
                "role":       role,
                "created_at": createdAt,
                "updated_at": updatedAt,
        }, nil</span>
}

// GetTestProduct returns a test product by ID
func GetTestProduct(t *testing.T, productID int) (map[string]interface{}, error) <span class="cov3" title="2">{
        var id, stock, createdBy int
        var name, description string
        var price float64
        var createdAt, updatedAt string

        err := database.DB.QueryRow(
                "SELECT id, name, description, price, stock, created_by, created_at, updated_at FROM products WHERE id = ?",
                productID,
        ).Scan(&amp;id, &amp;name, &amp;description, &amp;price, &amp;stock, &amp;createdBy, &amp;createdAt, &amp;updatedAt)

        if err != nil </span><span class="cov0" title="0">{
                return nil, err
        }</span>

        <span class="cov3" title="2">return map[string]interface{}{
                "id":          id,
                "name":        name,
                "description": description,
                "price":       price,
                "stock":       stock,
                "created_by":  createdBy,
                "created_at":  createdAt,
                "updated_at":  updatedAt,
        }, nil</span>
}

// CountRows returns the number of rows in a table
func CountRows(t *testing.T, tableName string) int <span class="cov0" title="0">{
        var count int
        err := database.DB.QueryRow(fmt.Sprintf("SELECT COUNT(*) FROM %s", tableName)).Scan(&amp;count)
        if err != nil </span><span class="cov0" title="0">{
                t.Fatalf("Failed to count rows in %s: %v", tableName, err)
        }</span>
        <span class="cov0" title="0">return count</span>
}

// ClearTable removes all rows from a table
func ClearTable(t *testing.T, tableName string) <span class="cov0" title="0">{
        _, err := database.DB.Exec(fmt.Sprintf("DELETE FROM %s", tableName))
        if err != nil </span><span class="cov0" title="0">{
                t.Fatalf("Failed to clear table %s: %v", tableName, err)
        }</span>
}
</pre>
		
		<pre class="file" id="file10" style="display: none">package websocket

import (
        "encoding/json"
        "log"
        "net/http"
        "smarapp-api/models"
        "time"

        "github.com/gorilla/websocket"
)

const (
        writeWait      = 10 * time.Second
        pongWait       = 60 * time.Second
        pingPeriod     = (pongWait * 9) / 10
        maxMessageSize = 512
)

var upgrader = websocket.Upgrader{
        ReadBufferSize:  1024,
        WriteBufferSize: 1024,
        CheckOrigin: func(r *http.Request) bool <span class="cov0" title="0">{
                return true // Allow all origins for development
        }</span>,
}

type Client struct {
        hub      *Hub
        conn     *websocket.Conn
        send     chan []byte
        UserID   int
        Username string
}

func (c *Client) readPump() <span class="cov0" title="0">{
        defer func() </span><span class="cov0" title="0">{
                c.hub.unregister &lt;- c
                c.conn.Close()
        }</span>()

        <span class="cov0" title="0">c.conn.SetReadLimit(maxMessageSize)
        c.conn.SetReadDeadline(time.Now().Add(pongWait))
        c.conn.SetPongHandler(func(string) error </span><span class="cov0" title="0">{
                c.conn.SetReadDeadline(time.Now().Add(pongWait))
                return nil
        }</span>)

        <span class="cov0" title="0">for </span><span class="cov0" title="0">{
                _, messageBytes, err := c.conn.ReadMessage()
                if err != nil </span><span class="cov0" title="0">{
                        if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) </span><span class="cov0" title="0">{
                                log.Printf("WebSocket error: %v", err)
                        }</span>
                        <span class="cov0" title="0">break</span>
                }

                <span class="cov0" title="0">var msg models.SendMessageRequest
                if err := json.Unmarshal(messageBytes, &amp;msg); err != nil </span><span class="cov0" title="0">{
                        log.Printf("Error unmarshaling message: %v", err)
                        continue</span>
                }

                // Save and broadcast the message
                <span class="cov0" title="0">if err := c.hub.SaveAndBroadcastMessage(c.UserID, c.Username, msg.Message); err != nil </span><span class="cov0" title="0">{
                        log.Printf("Error saving message: %v", err)
                }</span>
        }
}

func (c *Client) writePump() <span class="cov0" title="0">{
        ticker := time.NewTicker(pingPeriod)
        defer func() </span><span class="cov0" title="0">{
                ticker.Stop()
                c.conn.Close()
        }</span>()

        <span class="cov0" title="0">for </span><span class="cov0" title="0">{
                select </span>{
                case message, ok := &lt;-c.send:<span class="cov0" title="0">
                        c.conn.SetWriteDeadline(time.Now().Add(writeWait))
                        if !ok </span><span class="cov0" title="0">{
                                c.conn.WriteMessage(websocket.CloseMessage, []byte{})
                                return
                        }</span>

                        <span class="cov0" title="0">w, err := c.conn.NextWriter(websocket.TextMessage)
                        if err != nil </span><span class="cov0" title="0">{
                                return
                        }</span>
                        <span class="cov0" title="0">w.Write(message)

                        // Add queued chat messages to the current websocket message
                        n := len(c.send)
                        for i := 0; i &lt; n; i++ </span><span class="cov0" title="0">{
                                w.Write([]byte{'\n'})
                                w.Write(&lt;-c.send)
                        }</span>

                        <span class="cov0" title="0">if err := w.Close(); err != nil </span><span class="cov0" title="0">{
                                return
                        }</span>

                case &lt;-ticker.C:<span class="cov0" title="0">
                        c.conn.SetWriteDeadline(time.Now().Add(writeWait))
                        if err := c.conn.WriteMessage(websocket.PingMessage, nil); err != nil </span><span class="cov0" title="0">{
                                return
                        }</span>
                }
        }
}

func ServeWS(hub *Hub, w http.ResponseWriter, r *http.Request, userID int, username string) <span class="cov0" title="0">{
        conn, err := upgrader.Upgrade(w, r, nil)
        if err != nil </span><span class="cov0" title="0">{
                log.Printf("WebSocket upgrade error: %v", err)
                return
        }</span>

        <span class="cov0" title="0">client := &amp;Client{
                hub:      hub,
                conn:     conn,
                send:     make(chan []byte, 256),
                UserID:   userID,
                Username: username,
        }

        client.hub.register &lt;- client

        go client.writePump()
        go client.readPump()</span>
}
</pre>
		
		<pre class="file" id="file11" style="display: none">package websocket

import (
        "encoding/json"
        "log"
        "smarapp-api/database"
        "smarapp-api/models"
        "time"
)

type Hub struct {
        clients    map[*Client]bool
        broadcast  chan []byte
        register   chan *Client
        unregister chan *Client
}

func NewHub() *Hub <span class="cov10" title="2">{
        return &amp;Hub{
                clients:    make(map[*Client]bool),
                broadcast:  make(chan []byte),
                register:   make(chan *Client),
                unregister: make(chan *Client),
        }
}</span>

func (h *Hub) Run() <span class="cov0" title="0">{
        for </span><span class="cov0" title="0">{
                select </span>{
                case client := &lt;-h.register:<span class="cov0" title="0">
                        h.clients[client] = true
                        log.Printf("Client connected: %s (ID: %d)", client.Username, client.UserID)
                        
                        // Send join message to all clients
                        joinMsg := models.WebSocketMessage{
                                Type:    models.MessageTypeJoin,
                                UserID:  client.UserID,
                                Message: client.Username + " joined the chat",
                        }
                        h.broadcastMessage(joinMsg)
                        
                        // Send chat history to the new client
                        h.sendChatHistory(client)</span>

                case client := &lt;-h.unregister:<span class="cov0" title="0">
                        if _, ok := h.clients[client]; ok </span><span class="cov0" title="0">{
                                delete(h.clients, client)
                                close(client.send)
                                log.Printf("Client disconnected: %s (ID: %d)", client.Username, client.UserID)
                                
                                // Send leave message to all clients
                                leaveMsg := models.WebSocketMessage{
                                        Type:    models.MessageTypeLeave,
                                        UserID:  client.UserID,
                                        Message: client.Username + " left the chat",
                                }
                                h.broadcastMessage(leaveMsg)
                        }</span>

                case message := &lt;-h.broadcast:<span class="cov0" title="0">
                        for client := range h.clients </span><span class="cov0" title="0">{
                                select </span>{
                                case client.send &lt;- message:<span class="cov0" title="0"></span>
                                default:<span class="cov0" title="0">
                                        close(client.send)
                                        delete(h.clients, client)</span>
                                }
                        }
                }
        }
}

func (h *Hub) broadcastMessage(msg models.WebSocketMessage) <span class="cov0" title="0">{
        data, err := json.Marshal(msg)
        if err != nil </span><span class="cov0" title="0">{
                log.Printf("Error marshaling message: %v", err)
                return
        }</span>
        <span class="cov0" title="0">h.broadcast &lt;- data</span>
}

func (h *Hub) sendChatHistory(client *Client) <span class="cov0" title="0">{
        rows, err := database.DB.Query(`
                SELECT id, user_id, username, message, created_at 
                FROM chat_messages 
                ORDER BY created_at DESC 
                LIMIT 50
        `)
        if err != nil </span><span class="cov0" title="0">{
                log.Printf("Error fetching chat history: %v", err)
                return
        }</span>
        <span class="cov0" title="0">defer rows.Close()

        var messages []models.ChatMessage
        for rows.Next() </span><span class="cov0" title="0">{
                var msg models.ChatMessage
                err := rows.Scan(&amp;msg.ID, &amp;msg.UserID, &amp;msg.Username, &amp;msg.Message, &amp;msg.CreatedAt)
                if err != nil </span><span class="cov0" title="0">{
                        log.Printf("Error scanning chat message: %v", err)
                        continue</span>
                }
                <span class="cov0" title="0">messages = append(messages, msg)</span>
        }

        // Reverse to show oldest first
        <span class="cov0" title="0">for i := len(messages)/2 - 1; i &gt;= 0; i-- </span><span class="cov0" title="0">{
                opp := len(messages) - 1 - i
                messages[i], messages[opp] = messages[opp], messages[i]
        }</span>

        <span class="cov0" title="0">historyMsg := models.WebSocketMessage{
                Type: models.MessageTypeHistory,
                Data: messages,
        }

        data, err := json.Marshal(historyMsg)
        if err != nil </span><span class="cov0" title="0">{
                log.Printf("Error marshaling history: %v", err)
                return
        }</span>

        <span class="cov0" title="0">select </span>{
        case client.send &lt;- data:<span class="cov0" title="0"></span>
        default:<span class="cov0" title="0">
                close(client.send)
                delete(h.clients, client)</span>
        }
}

func (h *Hub) SaveAndBroadcastMessage(userID int, username, message string) error <span class="cov0" title="0">{
        // Save to database
        _, err := database.DB.Exec(
                "INSERT INTO chat_messages (user_id, username, message, created_at) VALUES (?, ?, ?, ?)",
                userID, username, message, time.Now(),
        )
        if err != nil </span><span class="cov0" title="0">{
                return err
        }</span>

        // Broadcast to all clients
        <span class="cov0" title="0">chatMsg := models.WebSocketMessage{
                Type:    models.MessageTypeChat,
                UserID:  userID,
                Message: message,
                Data: models.ChatMessage{
                        UserID:    userID,
                        Username:  username,
                        Message:   message,
                        CreatedAt: time.Now(),
                },
        }

        h.broadcastMessage(chatMsg)
        return nil</span>
}
</pre>
		
		</div>
	</body>
	<script>
	(function() {
		var files = document.getElementById('files');
		var visible;
		files.addEventListener('change', onChange, false);
		function select(part) {
			if (visible)
				visible.style.display = 'none';
			visible = document.getElementById(part);
			if (!visible)
				return;
			files.value = part;
			visible.style.display = 'block';
			location.hash = part;
		}
		function onChange() {
			select(files.value);
			window.scrollTo(0, 0);
		}
		if (location.hash != "") {
			select(location.hash.substr(1));
		}
		if (!visible) {
			select("file0");
		}
	})();
	</script>
</html>
