basePath: /api/v1
definitions:
  models.CreateOrderRequest:
    properties:
      product_id:
        type: integer
      quantity:
        type: integer
    required:
    - product_id
    - quantity
    type: object
  models.CreateProductRequest:
    properties:
      description:
        maxLength: 500
        minLength: 1
        type: string
      name:
        maxLength: 100
        minLength: 1
        type: string
      price:
        type: number
      stock:
        minimum: 0
        type: integer
    required:
    - description
    - name
    - price
    - stock
    type: object
  models.LoginRequest:
    properties:
      email:
        type: string
      password:
        minLength: 6
        type: string
    required:
    - email
    - password
    type: object
  models.LoginResponse:
    properties:
      token:
        type: string
      user:
        $ref: '#/definitions/models.User'
    type: object
  models.Order:
    properties:
      created_at:
        type: string
      id:
        type: integer
      price:
        description: Price at time of purchase
        type: number
      product_id:
        type: integer
      quantity:
        type: integer
      status:
        $ref: '#/definitions/models.OrderStatus'
      total:
        type: number
      updated_at:
        type: string
      user_id:
        type: integer
    type: object
  models.OrderResponse:
    properties:
      order:
        $ref: '#/definitions/models.Order'
      product:
        $ref: '#/definitions/models.Product'
    type: object
  models.OrderStatus:
    enum:
    - pending
    - completed
    - cancelled
    type: string
    x-enum-varnames:
    - OrderStatusPending
    - OrderStatusCompleted
    - OrderStatusCancelled
  models.Product:
    properties:
      created_at:
        type: string
      created_by:
        type: integer
      description:
        type: string
      id:
        type: integer
      name:
        type: string
      price:
        type: number
      stock:
        type: integer
      updated_at:
        type: string
    type: object
  models.RegisterRequest:
    properties:
      email:
        type: string
      password:
        minLength: 6
        type: string
      role:
        allOf:
        - $ref: '#/definitions/models.Role'
        description: Optional, defaults to user
      username:
        maxLength: 50
        minLength: 3
        type: string
    required:
    - email
    - password
    - username
    type: object
  models.Role:
    enum:
    - admin
    - user
    type: string
    x-enum-varnames:
    - RoleAdmin
    - RoleUser
  models.User:
    properties:
      created_at:
        type: string
      email:
        type: string
      id:
        type: integer
      role:
        $ref: '#/definitions/models.Role'
      updated_at:
        type: string
      username:
        type: string
    type: object
host: localhost:8081
info:
  contact:
    email: <EMAIL>
    name: API Support
    url: http://www.swagger.io/support
  description: A simple Go API with JWT authentication, role-based access control,
    WebSocket chat, and product management.
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT
  termsOfService: http://swagger.io/terms/
  title: SmarApp API
  version: "1.0"
paths:
  /auth/login:
    post:
      consumes:
      - application/json
      description: Authenticate user with email and password
      parameters:
      - description: User login credentials
        in: body
        name: credentials
        required: true
        schema:
          $ref: '#/definitions/models.LoginRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.LoginResponse'
        "400":
          description: Bad Request
          schema:
            additionalProperties:
              type: string
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Login user
      tags:
      - Authentication
  /auth/register:
    post:
      consumes:
      - application/json
      description: Register a new user with username, email, password and optional
        role
      parameters:
      - description: User registration data
        in: body
        name: user
        required: true
        schema:
          $ref: '#/definitions/models.RegisterRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            $ref: '#/definitions/models.LoginResponse'
        "400":
          description: Bad Request
          schema:
            additionalProperties:
              type: string
            type: object
        "409":
          description: Conflict
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Register a new user
      tags:
      - Authentication
  /orders:
    post:
      consumes:
      - application/json
      description: Create a new order to purchase a product, automatically reduces
        stock
      parameters:
      - description: Order data
        in: body
        name: order
        required: true
        schema:
          $ref: '#/definitions/models.CreateOrderRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            $ref: '#/definitions/models.OrderResponse'
        "400":
          description: Bad Request
          schema:
            additionalProperties:
              type: string
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties:
              type: string
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties:
              type: string
            type: object
      security:
      - BearerAuth: []
      summary: Create a new order (Buy a product)
      tags:
      - Orders
  /products:
    get:
      description: Get a list of all products
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/models.Product'
            type: array
        "500":
          description: Internal Server Error
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Get all products
      tags:
      - Products
    post:
      consumes:
      - application/json
      description: Create a new product with name, description, price and stock
      parameters:
      - description: Product data
        in: body
        name: product
        required: true
        schema:
          $ref: '#/definitions/models.CreateProductRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            $ref: '#/definitions/models.Product'
        "400":
          description: Bad Request
          schema:
            additionalProperties:
              type: string
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties:
              type: string
            type: object
        "403":
          description: Forbidden
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties:
              type: string
            type: object
      security:
      - BearerAuth: []
      summary: Create a new product (Admin only)
      tags:
      - Products
  /profile:
    get:
      description: Get the profile of the authenticated user
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.User'
        "401":
          description: Unauthorized
          schema:
            additionalProperties:
              type: string
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties:
              type: string
            type: object
      security:
      - BearerAuth: []
      summary: Get user profile
      tags:
      - Authentication
schemes:
- http
- https
securityDefinitions:
  BearerAuth:
    description: Type "Bearer" followed by a space and JWT token.
    in: header
    name: Authorization
    type: apiKey
swagger: "2.0"
